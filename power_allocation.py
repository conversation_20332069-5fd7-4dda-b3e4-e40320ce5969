import pandas as pd

def process_power_allocation(scheduled_generation):
    """
    Process and allocate scheduled power generation data across TEQ GREEN and O2 SPVs.
    
    Args:
        scheduled_generation: List of 96 values (15-min blocks for 24 hours)
    
    Returns:
        DataFrame with allocations for each time block
    """
    
    # Constants
    TOTAL_SPV_CAPACITY = 44.3  # MW
    TEQ_GREEN_SHARE = 27       # MW
    O2_SHARE = 17.3           # MW
    MIN_THRESHOLD = 7         # MW
    
    results = []
    
    for i, original_value in enumerate(scheduled_generation):
        # Step 1: Adjust each value (subtract 7, minimum 7)
        adjusted_value = max(original_value - 7, 7)
        total_schedule = adjusted_value
        
        # Step 2: Calculate TEQ GREEN's share
        teq_g_sch = ((total_schedule - 7) / TOTAL_SPV_CAPACITY) * TEQ_GREEN_SHARE
        
        # Step 3: Calculate O2's share
        o2_sch = total_schedule - teq_g_sch
        
        # Create time block label (15-min intervals)
        hour = i // 4
        minute = (i % 4) * 15
        time_block = f"{hour:02d}:{minute:02d}"
        
        results.append({
            'Time Block': time_block,
            'Original Schedule (MW)': original_value,
            'Adjusted Schedule (MW)': total_schedule,
            'TEQ G SCH (MW)': round(teq_g_sch, 2),
            'O2 SCH (MW)': round(o2_sch, 2)
        })
    
    return pd.DataFrame(results)

# Example usage with sample data
if __name__ == "__main__":
    # Sample input: 96 values for demonstration
    sample_data = [15, 20, 25, 30] * 24  # Simplified example
    
    # Process the data
    result_df = process_power_allocation(sample_data)
    
    # Display results
    print("Power Generation Allocation Results")
    print("=" * 60)
    print(result_df.to_string(index=False))
    
    # Summary statistics
    print(f"\nSummary:")
    print(f"Total TEQ GREEN allocation: {result_df['TEQ G SCH (MW)'].sum():.2f} MW")
    print(f"Total O2 allocation: {result_df['O2 SCH (MW)'].sum():.2f} MW")