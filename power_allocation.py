import streamlit as st
import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots

def process_power_allocation(scheduled_generation):
    """
    Process and allocate scheduled power generation data across TEQ GREEN and O2 SPVs.

    Args:
        scheduled_generation: List of 96 values (15-min blocks for 24 hours)

    Returns:
        DataFrame with allocations for each time block
    """

    # Constants
    TOTAL_SPV_CAPACITY = 44.3  # MW
    TEQ_GREEN_SHARE = 27       # MW
    O2_SHARE = 17.3           # MW
    MIN_THRESHOLD = 7         # MW

    results = []

    for i, original_value in enumerate(scheduled_generation):
        # Step 1: Adjust each value (subtract 7, minimum 7)
        adjusted_value = max(original_value - 7, 7)
        total_schedule = adjusted_value

        # Step 2: Calculate TEQ GREEN's share
        teq_g_sch = ((total_schedule - 7) / TOTAL_SPV_CAPACITY) * TEQ_GREEN_SHARE

        # Step 3: Calculate O2's share
        o2_sch = total_schedule - teq_g_sch

        # Create time block label (15-min intervals)
        hour = i // 4
        minute = (i % 4) * 15
        time_block = f"{hour:02d}:{minute:02d}"

        results.append({
            'Time Block': time_block,
            'Original Schedule (MW)': original_value,
            'Adjusted Schedule (MW)': total_schedule,
            'TEQ G SCH (MW)': round(teq_g_sch, 2),
            'O2 SCH (MW)': round(o2_sch, 2)
        })

    return pd.DataFrame(results)

def main():
    st.set_page_config(
        page_title="Power Allocation Dashboard",
        page_icon="⚡",
        layout="wide",
        initial_sidebar_state="expanded"
    )

    st.title("⚡ Power Generation Allocation Dashboard")
    st.markdown("### TEQ GREEN and O2 SPV Power Allocation System")

    # Sidebar for configuration
    st.sidebar.header("Configuration")

    # Constants display and editing
    st.sidebar.subheader("System Parameters")
    total_capacity = st.sidebar.number_input("Total SPV Capacity (MW)", value=44.3, min_value=0.0, step=0.1)
    teq_green_share = st.sidebar.number_input("TEQ GREEN Share (MW)", value=27.0, min_value=0.0, step=0.1)
    o2_share = st.sidebar.number_input("O2 Share (MW)", value=17.3, min_value=0.0, step=0.1)
    min_threshold = st.sidebar.number_input("Minimum Threshold (MW)", value=7.0, min_value=0.0, step=0.1)

    # Data input options
    st.sidebar.subheader("Data Input Options")
    input_method = st.sidebar.radio(
        "Choose input method:",
        ["Sample Data", "Manual Input", "Upload CSV"]
    )

    scheduled_generation = None

    if input_method == "Sample Data":
        st.sidebar.write("Using sample data pattern")
        pattern_type = st.sidebar.selectbox(
            "Select pattern:",
            ["Constant", "Linear Ramp", "Sine Wave", "Random"]
        )

        if pattern_type == "Constant":
            base_value = st.sidebar.slider("Base Value (MW)", 7, 50, 20)
            scheduled_generation = [base_value] * 96
        elif pattern_type == "Linear Ramp":
            start_val = st.sidebar.slider("Start Value (MW)", 7, 50, 10)
            end_val = st.sidebar.slider("End Value (MW)", 7, 50, 40)
            scheduled_generation = np.linspace(start_val, end_val, 96).tolist()
        elif pattern_type == "Sine Wave":
            amplitude = st.sidebar.slider("Amplitude", 5, 20, 10)
            offset = st.sidebar.slider("Offset (MW)", 15, 35, 25)
            scheduled_generation = [offset + amplitude * np.sin(2 * np.pi * i / 96) for i in range(96)]
        else:  # Random
            min_val = st.sidebar.slider("Min Value (MW)", 7, 30, 10)
            max_val = st.sidebar.slider("Max Value (MW)", 20, 50, 35)
            np.random.seed(42)  # For reproducibility
            scheduled_generation = np.random.uniform(min_val, max_val, 96).tolist()

    elif input_method == "Manual Input":
        st.sidebar.write("Enter 96 values (comma-separated)")
        manual_input = st.sidebar.text_area(
            "Power values:",
            value="15,20,25,30," * 24,
            height=100
        )
        try:
            scheduled_generation = [float(x.strip()) for x in manual_input.split(',') if x.strip()]
            if len(scheduled_generation) != 96:
                st.sidebar.error(f"Please enter exactly 96 values. Currently have {len(scheduled_generation)} values.")
                scheduled_generation = None
        except ValueError:
            st.sidebar.error("Please enter valid numeric values separated by commas.")
            scheduled_generation = None

    else:  # Upload CSV
        uploaded_file = st.sidebar.file_uploader("Choose a CSV file", type="csv")
        if uploaded_file is not None:
            try:
                df = pd.read_csv(uploaded_file)
                if len(df.columns) > 0:
                    column = st.sidebar.selectbox("Select column with power values:", df.columns)
                    scheduled_generation = df[column].tolist()
                    if len(scheduled_generation) != 96:
                        st.sidebar.warning(f"Expected 96 values, got {len(scheduled_generation)}. Using first 96 or padding with zeros.")
                        if len(scheduled_generation) > 96:
                            scheduled_generation = scheduled_generation[:96]
                        else:
                            scheduled_generation.extend([0] * (96 - len(scheduled_generation)))
            except Exception as e:
                st.sidebar.error(f"Error reading CSV: {str(e)}")
                scheduled_generation = None

    # Main content
    if scheduled_generation is not None:
        # Process the data
        result_df = process_power_allocation(scheduled_generation)

        # Create tabs for different views
        tab1, tab2, tab3, tab4 = st.tabs(["📊 Overview", "📈 Charts", "📋 Data Table", "📊 Summary"])

        with tab1:
            # Key metrics
            col1, col2, col3, col4 = st.columns(4)

            with col1:
                st.metric(
                    "Total TEQ GREEN",
                    f"{result_df['TEQ G SCH (MW)'].sum():.2f} MW",
                    f"{result_df['TEQ G SCH (MW)'].mean():.2f} MW avg"
                )

            with col2:
                st.metric(
                    "Total O2",
                    f"{result_df['O2 SCH (MW)'].sum():.2f} MW",
                    f"{result_df['O2 SCH (MW)'].mean():.2f} MW avg"
                )

            with col3:
                st.metric(
                    "Peak Original",
                    f"{result_df['Original Schedule (MW)'].max():.2f} MW",
                    f"at {result_df.loc[result_df['Original Schedule (MW)'].idxmax(), 'Time Block']}"
                )

            with col4:
                st.metric(
                    "Total Adjusted",
                    f"{result_df['Adjusted Schedule (MW)'].sum():.2f} MW",
                    f"{result_df['Adjusted Schedule (MW)'].mean():.2f} MW avg"
                )

            # Quick visualization
            st.subheader("Power Allocation Overview")
            fig = make_subplots(
                rows=2, cols=1,
                subplot_titles=('Original vs Adjusted Schedule', 'TEQ GREEN vs O2 Allocation'),
                vertical_spacing=0.1
            )

            # Add time index for x-axis
            time_indices = list(range(len(result_df)))

            # Top subplot - Original vs Adjusted
            fig.add_trace(
                go.Scatter(x=time_indices, y=result_df['Original Schedule (MW)'],
                          name='Original Schedule', line=dict(color='blue')),
                row=1, col=1
            )
            fig.add_trace(
                go.Scatter(x=time_indices, y=result_df['Adjusted Schedule (MW)'],
                          name='Adjusted Schedule', line=dict(color='red')),
                row=1, col=1
            )

            # Bottom subplot - TEQ GREEN vs O2
            fig.add_trace(
                go.Scatter(x=time_indices, y=result_df['TEQ G SCH (MW)'],
                          name='TEQ GREEN', line=dict(color='green')),
                row=2, col=1
            )
            fig.add_trace(
                go.Scatter(x=time_indices, y=result_df['O2 SCH (MW)'],
                          name='O2', line=dict(color='orange')),
                row=2, col=1
            )

            fig.update_layout(height=600, showlegend=True)
            fig.update_xaxes(title_text="Time Block Index")
            fig.update_yaxes(title_text="Power (MW)")

            st.plotly_chart(fig, use_container_width=True)

        with tab2:
            st.subheader("Detailed Charts")

            # Chart selection
            chart_type = st.selectbox(
                "Select chart type:",
                ["Line Chart", "Area Chart", "Bar Chart", "Stacked Area"]
            )

            if chart_type == "Line Chart":
                fig = go.Figure()
                fig.add_trace(go.Scatter(x=result_df['Time Block'], y=result_df['Original Schedule (MW)'],
                                       name='Original Schedule', mode='lines'))
                fig.add_trace(go.Scatter(x=result_df['Time Block'], y=result_df['Adjusted Schedule (MW)'],
                                       name='Adjusted Schedule', mode='lines'))
                fig.add_trace(go.Scatter(x=result_df['Time Block'], y=result_df['TEQ G SCH (MW)'],
                                       name='TEQ GREEN', mode='lines'))
                fig.add_trace(go.Scatter(x=result_df['Time Block'], y=result_df['O2 SCH (MW)'],
                                       name='O2', mode='lines'))

            elif chart_type == "Area Chart":
                fig = go.Figure()
                fig.add_trace(go.Scatter(x=result_df['Time Block'], y=result_df['TEQ G SCH (MW)'],
                                       fill='tonexty', name='TEQ GREEN'))
                fig.add_trace(go.Scatter(x=result_df['Time Block'], y=result_df['O2 SCH (MW)'],
                                       fill='tonexty', name='O2'))

            elif chart_type == "Bar Chart":
                # Show every 8th time block to avoid overcrowding
                sample_df = result_df.iloc[::8].copy()
                fig = go.Figure()
                fig.add_trace(go.Bar(x=sample_df['Time Block'], y=sample_df['TEQ G SCH (MW)'],
                                   name='TEQ GREEN'))
                fig.add_trace(go.Bar(x=sample_df['Time Block'], y=sample_df['O2 SCH (MW)'],
                                   name='O2'))

            else:  # Stacked Area
                fig = go.Figure()
                fig.add_trace(go.Scatter(x=result_df['Time Block'], y=result_df['TEQ G SCH (MW)'],
                                       fill='tozeroy', name='TEQ GREEN'))
                fig.add_trace(go.Scatter(x=result_df['Time Block'], y=result_df['TEQ G SCH (MW)'] + result_df['O2 SCH (MW)'],
                                       fill='tonexty', name='O2'))

            fig.update_layout(
                title=f"Power Allocation - {chart_type}",
                xaxis_title="Time Block",
                yaxis_title="Power (MW)",
                height=500
            )

            # Show every 12th time block label to avoid overcrowding
            if len(result_df) > 20:
                fig.update_xaxes(
                    tickmode='array',
                    tickvals=result_df['Time Block'][::12],
                    ticktext=result_df['Time Block'][::12]
                )

            st.plotly_chart(fig, use_container_width=True)

            # Distribution charts
            col1, col2 = st.columns(2)

            with col1:
                st.subheader("TEQ GREEN Distribution")
                fig_hist1 = px.histogram(result_df, x='TEQ G SCH (MW)', nbins=20,
                                       title="TEQ GREEN Power Distribution")
                st.plotly_chart(fig_hist1, use_container_width=True)

            with col2:
                st.subheader("O2 Distribution")
                fig_hist2 = px.histogram(result_df, x='O2 SCH (MW)', nbins=20,
                                       title="O2 Power Distribution")
                st.plotly_chart(fig_hist2, use_container_width=True)

        with tab3:
            st.subheader("Detailed Data Table")

            # Add filters
            col1, col2 = st.columns(2)
            with col1:
                min_power = st.slider("Minimum Original Power (MW)",
                                    float(result_df['Original Schedule (MW)'].min()),
                                    float(result_df['Original Schedule (MW)'].max()),
                                    float(result_df['Original Schedule (MW)'].min()))
            with col2:
                max_power = st.slider("Maximum Original Power (MW)",
                                    float(result_df['Original Schedule (MW)'].min()),
                                    float(result_df['Original Schedule (MW)'].max()),
                                    float(result_df['Original Schedule (MW)'].max()))

            # Filter data
            filtered_df = result_df[
                (result_df['Original Schedule (MW)'] >= min_power) &
                (result_df['Original Schedule (MW)'] <= max_power)
            ]

            st.write(f"Showing {len(filtered_df)} of {len(result_df)} time blocks")
            st.dataframe(filtered_df, use_container_width=True)

            # Download button
            csv = filtered_df.to_csv(index=False)
            st.download_button(
                label="Download filtered data as CSV",
                data=csv,
                file_name="power_allocation_results.csv",
                mime="text/csv"
            )

        with tab4:
            st.subheader("Summary Statistics")

            # Summary statistics
            col1, col2 = st.columns(2)

            with col1:
                st.write("**TEQ GREEN Statistics**")
                teq_stats = result_df['TEQ G SCH (MW)'].describe()
                st.write(teq_stats)

                st.write("**Original Schedule Statistics**")
                orig_stats = result_df['Original Schedule (MW)'].describe()
                st.write(orig_stats)

            with col2:
                st.write("**O2 Statistics**")
                o2_stats = result_df['O2 SCH (MW)'].describe()
                st.write(o2_stats)

                st.write("**Adjusted Schedule Statistics**")
                adj_stats = result_df['Adjusted Schedule (MW)'].describe()
                st.write(adj_stats)

            # Allocation percentages
            st.subheader("Allocation Analysis")
            total_teq = result_df['TEQ G SCH (MW)'].sum()
            total_o2 = result_df['O2 SCH (MW)'].sum()
            total_all = total_teq + total_o2

            col1, col2, col3 = st.columns(3)
            with col1:
                st.metric("TEQ GREEN %", f"{(total_teq/total_all)*100:.1f}%")
            with col2:
                st.metric("O2 %", f"{(total_o2/total_all)*100:.1f}%")
            with col3:
                st.metric("Total Allocated", f"{total_all:.2f} MW")

            # Peak hours analysis
            st.subheader("Peak Hours Analysis")
            peak_original = result_df.loc[result_df['Original Schedule (MW)'].idxmax()]
            peak_teq = result_df.loc[result_df['TEQ G SCH (MW)'].idxmax()]
            peak_o2 = result_df.loc[result_df['O2 SCH (MW)'].idxmax()]

            peak_data = pd.DataFrame({
                'Metric': ['Peak Original Schedule', 'Peak TEQ GREEN', 'Peak O2'],
                'Time Block': [peak_original['Time Block'], peak_teq['Time Block'], peak_o2['Time Block']],
                'Value (MW)': [peak_original['Original Schedule (MW)'], peak_teq['TEQ G SCH (MW)'], peak_o2['O2 SCH (MW)']]
            })

            st.dataframe(peak_data, use_container_width=True)

    else:
        st.info("Please configure the input data in the sidebar to see the power allocation results.")
        st.markdown("""
        ### How to use this application:

        1. **Configure System Parameters**: Adjust the SPV capacity, shares, and threshold values in the sidebar
        2. **Choose Input Method**:
           - **Sample Data**: Use predefined patterns for testing
           - **Manual Input**: Enter 96 comma-separated values for 24-hour schedule
           - **Upload CSV**: Upload a CSV file with power generation data
        3. **View Results**: Explore the different tabs to see overview, charts, data table, and summary statistics

        ### About the Allocation Algorithm:

        The system processes 96 time blocks (15-minute intervals for 24 hours) and:
        1. Adjusts each value by subtracting 7 MW (minimum threshold)
        2. Calculates TEQ GREEN's proportional share based on capacity
        3. Allocates remaining power to O2
        """)

if __name__ == "__main__":
    main()