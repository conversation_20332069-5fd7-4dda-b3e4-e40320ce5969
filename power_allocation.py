import streamlit as st
import pandas as pd
import numpy as np
import plotly.graph_objects as go

def allocate_power(scheduled_generation, target_spv="TEQ GREEN"):
    total_capacity = 44.3
    teq_share = 27.0
    min_threshold = 7.0
    results = []

    for i, original in enumerate(scheduled_generation):
        adjusted = original - min_threshold
        if adjusted < min_threshold:
            adjusted = min_threshold
        total_schedule = adjusted
        # Use provided formula for both allocation types
        teq_g_sch = ((total_schedule - 7) / 44.3) * 27
        o2_sch = total_schedule - teq_g_sch

        hour = i // 4
        minute = (i % 4) * 15
        time_block = f"{hour:02d}:{minute:02d}"

        results.append({
            'Time Block': time_block,
            'Original Schedule (MW)': original,
            'Adjusted Schedule (MW)': adjusted,
            'TEQ G SCH (MW)': round(teq_g_sch, 2),
            'O2 SCH (MW)': round(o2_sch, 2)
        })

    return pd.DataFrame(results)

def main():
    st.set_page_config(page_title="SPV Power Allocation", layout="wide")
    st.title("⚡ Power Allocation for TEQ GREEN & O2")

    st.sidebar.header("📥 Input Configuration")
    target_spv = st.sidebar.radio("Allocate based on", ["TEQ GREEN", "O2"])
    input_method = st.sidebar.radio("Input Method", ["Sample Data", "Manual Input", "Upload CSV"])

    scheduled = []

    if input_method == "Sample Data":
        base = st.sidebar.slider("Base Value (MW)", 10, 50, 20)
        scheduled = [base] * 96

    elif input_method == "Manual Input":
        text = st.sidebar.text_area("Enter 96 values (one per line)", value="\n".join(["20"] * 96))
        try:
            scheduled = [float(x.strip()) for x in text.splitlines() if x.strip()]
            if len(scheduled) != 96:
                st.error(f"Expected 96 values, got {len(scheduled)}")
                return
        except ValueError:
            st.error("Please enter only numeric values.")
            return

    elif input_method == "Upload CSV":
        file = st.sidebar.file_uploader("Upload CSV File", type="csv")
        if file:
            df = pd.read_csv(file)
            column = st.sidebar.selectbox("Select Column", df.columns)
            scheduled = df[column].tolist()
            if len(scheduled) != 96:
                st.warning(f"Expected 96 values. Got {len(scheduled)}. Adjusting...")
                scheduled = (scheduled + [0]*96)[:96]

    if scheduled:
        result_df = allocate_power(scheduled, target_spv)

        st.subheader("📊 Power Allocation Table")
        st.dataframe(result_df, use_container_width=True)

        st.subheader("📈 Allocation Chart")
        fig = go.Figure()
        fig.add_trace(go.Scatter(x=result_df["Time Block"], y=result_df["Original Schedule (MW)"], name="Original"))
        fig.add_trace(go.Scatter(x=result_df["Time Block"], y=result_df["Adjusted Schedule (MW)"], name="Adjusted"))
        fig.add_trace(go.Scatter(x=result_df["Time Block"], y=result_df["TEQ G SCH (MW)"], name="TEQ GREEN"))
        fig.add_trace(go.Scatter(x=result_df["Time Block"], y=result_df["O2 SCH (MW)"], name="O2"))
        fig.update_layout(xaxis_title="Time Block", yaxis_title="MW", height=500)
        st.plotly_chart(fig, use_container_width=True)

        csv = result_df.to_csv(index=False)
        st.download_button("Download as CSV", data=csv, file_name="power_allocation_output.csv", mime="text/csv")
    else:
        st.info("Provide valid input to generate results.")

if __name__ == "__main__":
    main()